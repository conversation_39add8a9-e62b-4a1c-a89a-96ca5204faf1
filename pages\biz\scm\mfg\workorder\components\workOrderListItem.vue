<template>
	<view class="work-order-item">
		<view class="item-container" @click="handleItemClick">
			<!-- 头部信息：生产单号和状态 -->
			<view class="header-section">
				<view class="work-no-container">
					<text class="work-no">{{ item.workNo || '暂无单号' }}</text>
					<view class="copy-btn" @click.stop="copyWorkNo" v-if="item.workNo">
						<uni-icons type="copy" size="14" color="#666"></uni-icons>
					</view>
				</view>
				<view class="status-badges">
					<view class="status-tag" :style="{ backgroundColor: getStatusColor(item.status, 'work_order_status') }">
						{{ getStatusText(item.status) }}
					</view>
					<view class="approve-tag" :style="{ backgroundColor: getStatusColor(item.approveStatus, 'approve_status') }" v-if="item.approveStatus !== undefined">
						{{ getApproveStatusText(item.approveStatus) }}
					</view>
				</view>
			</view>

			<!-- 产品信息 -->
			<view class="product-section">
				<view class="product-info">
					<text class="product-name">{{ item.productName || '未知产品' }}</text>
					<text class="product-code" v-if="item.productCode">{{ item.productCode }}</text>
				</view>
				<view class="product-spec" v-if="item.spec">
					<text class="spec-text">{{ item.spec }}</text>
				</view>
			</view>

			<!-- 数量和时间信息 -->
			<view class="details-section">
				<view class="detail-row">
					<view class="detail-item">
						<text class="detail-label">订单数量</text>
						<text class="detail-value">{{ formatQuantity(item.orderQuantity) }} {{ getUnitText(item.orderUnit) }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">计划数量</text>
						<text class="detail-value">{{ formatQuantity(item.scheduleQuantity) }} {{ getUnitText(item.orderUnit) }}</text>
					</view>
				</view>

				<view class="detail-row" v-if="item.scheduleStartTime || item.scheduleEndTime">
					<view class="detail-item">
						<text class="detail-label">开始时间</text>
						<text class="detail-value">{{ formatDateTime(item.scheduleStartTime) }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">结束时间</text>
						<text class="detail-value">{{ formatDateTime(item.scheduleEndTime) }}</text>
					</view>
				</view>
			</view>

			<!-- 进度条 -->
			<view class="progress-section" v-if="item.progress !== undefined && item.progress !== null">
				<view class="progress-header">
					<text class="progress-label">完成进度</text>
					<text class="progress-text">{{ formatProgress(item.progress) }}%</text>
				</view>
				<view class="progress-bar">
					<view class="progress-fill" :style="{ width: formatProgress(item.progress) + '%', backgroundColor: getProgressColor(item.progress) }"></view>
				</view>
			</view>

			<!-- 其他状态信息 -->
			<view class="status-section" v-if="hasStatusInfo">
				<view class="status-row">
					<view class="status-item" v-if="item.pickingStatus !== undefined">
						<text class="status-label">领料</text>
						<view class="status-badge" :style="{ backgroundColor: getStatusColor(item.pickingStatus, 'common_task_status') }">
							{{ getTaskStatusText(item.pickingStatus) }}
						</view>
					</view>
					<view class="status-item" v-if="item.reportStatus !== undefined">
						<text class="status-label">报工</text>
						<view class="status-badge" :style="{ backgroundColor: getStatusColor(item.reportStatus, 'common_task_status') }">
							{{ getTaskStatusText(item.reportStatus) }}
						</view>
					</view>
					<view class="status-item" v-if="item.qualityStatus !== undefined">
						<text class="status-label">质检</text>
						<view class="status-badge" :style="{ backgroundColor: getStatusColor(item.qualityStatus, 'common_task_status') }">
							{{ getTaskStatusText(item.qualityStatus) }}
						</view>
					</view>
					<view class="status-item" v-if="item.inStockStatus !== undefined">
						<text class="status-label">入库</text>
						<view class="status-badge" :style="{ backgroundColor: getStatusColor(item.inStockStatus, 'common_task_status') }">
							{{ getTaskStatusText(item.inStockStatus) }}
						</view>
					</view>
				</view>
			</view>

			<!-- 底部信息 -->
			<view class="footer-section" v-if="item.customerName || item.orderNo || item.createTime">
				<view class="footer-row" v-if="item.customerName">
					<text class="footer-label">客户：</text>
					<text class="footer-value">{{ item.customerName }}</text>
				</view>
				<view class="footer-row" v-if="item.orderNo">
					<text class="footer-label">来源单号：</text>
					<text class="footer-value">{{ item.orderNo }}</text>
				</view>
				<view class="footer-row" v-if="item.createTime">
					<text class="footer-label">创建时间：</text>
					<text class="footer-value">{{ formatDateTime(item.createTime) }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { getDictOptions, getDictLabel, getBatchDictOptions, DICT_TYPE } from '../../../../../../utils/dict';

export default {
	name: 'WorkOrderListItem',
	props: {
		item: {
			type: Object,
			required: true,
			default: () => ({})
		}
	},
	data() {
		return {
			// 字典数据
			dictOptions: {
				mfg_status: [],           // 生产状态
				approve_status: [],       // 审批状态
				common_task_status: [],   // 通用任务状态
				material_unit: []         // 物料单位
			}
		}
	},
	computed: {
		// 是否有状态信息需要显示
		hasStatusInfo() {
			return this.item.pickingStatus !== undefined ||
				   this.item.reportStatus !== undefined ||
				   this.item.qualityStatus !== undefined ||
				   this.item.inStockStatus !== undefined;
		}
	},

	async mounted() {
		await this.initDictData();
	},

	methods: {
		// 初始化字典数据
		async initDictData() {
			try {
				const dictTypes = [
					DICT_TYPE.WORK_ORDER_STATUS,    // 生产任务单状态
					DICT_TYPE.APPROVE_STATUS,       // 审批状态
					DICT_TYPE.COMMON_TASK_STATUS,   // 通用任务状态
					DICT_TYPE.MATERIAL_UNIT         // 物料单位
				];
				const dictMap = await getBatchDictOptions(dictTypes);

				this.dictOptions = {
					work_order_status: dictMap[DICT_TYPE.WORK_ORDER_STATUS] || [],
					approve_status: dictMap[DICT_TYPE.APPROVE_STATUS] || [],
					common_task_status: dictMap[DICT_TYPE.COMMON_TASK_STATUS] || [],
					material_unit: dictMap[DICT_TYPE.MATERIAL_UNIT] || []
				};
			} catch (error) {
				console.error('获取字典数据失败:', error);
				// 保持空数组，getDictLabel会返回原始值
				this.dictOptions = {
					work_order_status: [],
					approve_status: [],
					common_task_status: [],
					material_unit: []
				};
			}
		},
		// 处理列表项点击
		handleItemClick() {
			this.$emit('click', this.item);
		},

		// 复制生产单号
		copyWorkNo() {
			if (this.item.workNo) {
				// #ifdef H5
				if (navigator.clipboard) {
					navigator.clipboard.writeText(this.item.workNo).then(() => {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						});
					});
				} else {
					// 降级方案
					const textArea = document.createElement('textarea');
					textArea.value = this.item.workNo;
					document.body.appendChild(textArea);
					textArea.select();
					document.execCommand('copy');
					document.body.removeChild(textArea);
					uni.showToast({
						title: '复制成功',
						icon: 'success'
					});
				}
				// #endif

				// #ifdef MP-WEIXIN
				uni.setClipboardData({
					data: this.item.workNo,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						});
					}
				});
				// #endif

				// #ifdef APP-PLUS
				uni.setClipboardData({
					data: this.item.workNo,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						});
					}
				});
				// #endif
			}
		},

		// 获取生产状态文本
		getStatusText(status) {
			return getDictLabel(this.dictOptions.work_order_status, status) || '未知状态';
		},

		// 获取审批状态文本
		getApproveStatusText(status) {
			return getDictLabel(this.dictOptions.approve_status, status) || '未知';
		},

		// 获取任务状态文本
		getTaskStatusText(status) {
			return getDictLabel(this.dictOptions.common_task_status, status) || '未知';
		},

		// 格式化数量
		formatQuantity(value) {
			if (value === null || value === undefined || value === '') return '0';
			if (isNaN(value)) return '0';
			return Number(value).toLocaleString();
		},

		// 获取单位文本
		getUnitText(unit) {
			if (!unit) return '';
			// 使用字典数据获取单位文本
			return getDictLabel(this.dictOptions.material_unit, unit) || unit;
		},

		// 格式化日期时间
		formatDateTime(timestamp) {
			if (!timestamp) return '-';
			const date = new Date(timestamp);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			const hours = String(date.getHours()).padStart(2, '0');
			const minutes = String(date.getMinutes()).padStart(2, '0');
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		},

		// 格式化进度
		formatProgress(progress) {
			if (progress === null || progress === undefined) return 0;
			const num = parseFloat(progress);
			return isNaN(num) ? 0 : Math.min(100, Math.max(0, num));
		},

		// 获取进度条颜色
		getProgressColor(progress) {
			const num = this.formatProgress(progress);
			if (num >= 100) return '#67c23a'; // 绿色 - 已完成
			if (num >= 80) return '#409eff';  // 蓝色 - 接近完成
			if (num >= 50) return '#e6a23c';  // 橙色 - 进行中
			if (num > 0) return '#f56c6c';    // 红色 - 刚开始
			return '#dcdfe6';                 // 灰色 - 未开始
		},

		// 获取状态标签的样式类
		getStatusClass(status, type = 'status') {
			const statusStr = String(status);
			return `${type}-${statusStr}`;
		},

		// 获取状态标签的颜色
		getStatusColor(status, dictType) {
			const dictOption = this.dictOptions[dictType]?.find(item => String(item.value) === String(status));
			if (dictOption && dictOption.colorType) {
				// 根据字典中的colorType返回对应颜色
				const colorMap = {
					'primary': '#409eff',
					'success': '#67c23a',
					'warning': '#e6a23c',
					'danger': '#f56c6c',
					'info': '#909399'
				};
				return colorMap[dictOption.colorType] || '#909399';
			}

			// 默认颜色映射
			const defaultColors = {
				'0': '#909399', // 灰色
				'1': '#e6a23c', // 橙色
				'2': '#409eff', // 蓝色
				'3': '#f56c6c', // 红色
				'4': '#67c23a', // 绿色
				'5': '#c0c4cc'  // 浅灰色
			};
			return defaultColors[String(status)] || '#909399';
		}
	}
}
</script>

<style lang="scss" scoped>
.work-order-item {
	margin-bottom: 24rpx;
	background-color: white;
	border-radius: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
	border: 1px solid #f0f0f0;
	overflow: hidden;
	transition: all 0.3s ease;
	position: relative;

	/* 添加微妙的渐变背景 */
	background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);

	&:active {
		transform: translateY(2rpx) scale(0.98);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.12);
	}

	/* 添加左侧装饰条 */
	&::before {
		content: '';
		position: absolute;
		left: 0;
		top: 0;
		bottom: 0;
		width: 6rpx;
		background: linear-gradient(to bottom, #409eff, #67c23a);
		border-radius: 0 6rpx 6rpx 0;
	}
}

.item-container {
	padding: 28rpx;
}

/* 头部区域 */
.header-section {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 24rpx;
}

.work-no-container {
	display: flex;
	align-items: center;
	flex: 1;

	.work-no {
		font-size: 32rpx;
		font-weight: 600;
		color: #2c3e50;
		margin-right: 16rpx;
	}

	.copy-btn {
		padding: 8rpx;
		border-radius: 8rpx;
		background-color: #f8f9fa;
		transition: background-color 0.2s;

		&:active {
			background-color: #e9ecef;
		}
	}
}

.status-badges {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 8rpx;
}

.status-tag, .approve-tag {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	font-weight: 500;
	color: white;
	text-align: center;
	min-width: 120rpx;
}

/* 状态标签通用样式 - 移除硬编码颜色，使用动态样式 */
.status-tag, .approve-tag {
	/* 添加渐变效果和阴影 */
	background: linear-gradient(135deg, var(--status-color, #909399) 0%, var(--status-color, #909399) 100%);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
		box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.15);
	}
}

/* 产品信息区域 */
.product-section {
	margin-bottom: 24rpx;
	padding: 20rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
	border-radius: 16rpx;
	border: 1px solid #e9ecef;
}

.product-info {
	display: flex;
	align-items: center;
	margin-bottom: 12rpx;

	.product-name {
		font-size: 30rpx;
		font-weight: 600;
		color: #2c3e50;
		margin-right: 16rpx;
		flex: 1;
		line-height: 1.4;
	}

	.product-code {
		font-size: 24rpx;
		color: #495057;
		background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
		padding: 8rpx 16rpx;
		border-radius: 12rpx;
		border: 1px solid #e1f5fe;
		font-weight: 500;
	}
}

.product-spec {
	.spec-text {
		font-size: 26rpx;
		color: #6c757d;
		font-style: italic;
	}
}

/* 详情区域 */
.details-section {
	margin-bottom: 24rpx;
}

.detail-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.detail-item {
	flex: 1;

	.detail-label {
		font-size: 24rpx;
		color: #6c757d;
		display: block;
		margin-bottom: 6rpx;
	}

	.detail-value {
		font-size: 26rpx;
		color: #2c3e50;
		font-weight: 500;
	}
}

/* 进度区域 */
.progress-section {
	margin-bottom: 24rpx;
	padding: 20rpx;
	background: linear-gradient(135deg, #f1f8ff 0%, #fff5f5 100%);
	border-radius: 16rpx;
	border: 1px solid #e3f2fd;
}

.progress-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;

	.progress-label {
		font-size: 26rpx;
		color: #6c757d;
		font-weight: 500;
	}

	.progress-text {
		font-size: 28rpx;
		color: #2c3e50;
		font-weight: 700;
		background: linear-gradient(135deg, #409eff, #67c23a);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}
}

.progress-bar {
	height: 16rpx;
	background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
	border-radius: 8rpx;
	overflow: hidden;
	box-shadow: inset 0 2rpx 4rpx rgba(0, 0, 0, 0.1);

	.progress-fill {
		height: 100%;
		border-radius: 8rpx;
		transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
		background: linear-gradient(135deg, var(--progress-color, #409eff) 0%, var(--progress-color, #67c23a) 100%);
		box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
		position: relative;

		&::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
			animation: shimmer 2s infinite;
		}
	}
}

@keyframes shimmer {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

/* 状态区域 */
.status-section {
	margin-bottom: 24rpx;
}

.status-row {
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.status-item {
	display: flex;
	align-items: center;
	gap: 8rpx;

	.status-label {
		font-size: 24rpx;
		color: #6c757d;
	}

	.status-badge {
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
		font-size: 22rpx;
		font-weight: 500;
		color: white;
	}
}

/* 任务状态标签样式 - 使用动态颜色 */
.status-badge {
	transition: all 0.2s ease;
	box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);

	&:active {
		transform: scale(0.95);
	}
}

/* 底部区域 */
.footer-section {
	border-top: 1px solid #f0f0f0;
	padding-top: 20rpx;
}

.footer-row {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;

	&:last-child {
		margin-bottom: 0;
	}

	.footer-label {
		font-size: 24rpx;
		color: #6c757d;
		min-width: 140rpx;
	}

	.footer-value {
		font-size: 24rpx;
		color: #2c3e50;
		flex: 1;
		word-break: break-all;
	}
}
</style>