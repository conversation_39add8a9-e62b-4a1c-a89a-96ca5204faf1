import request from "../../../../utils/request";

// 查询生产任务分页
export function getWorkOrderPageApi(params) {
	return request({
		url: '/scm/mfg/work-order/page',
		method: 'GET',
		params
	})
}

// 查询生产任务详情
export function getWorkOrderApi(id) {
	return request({
		url: '/scm/mfg/work-order/get?id=' + id,
		method: 'GET'
	})
}

// 新增生产任务
export function createWorkOrderApi(data) {
	return request({
		url: '/scm/mfg/work-order/create',
		method: 'POST',
		data
	})
}

// 修改生产任务
export function updateWorkOrderApi(data) {
	return request({
		url: '/scm/mfg/work-order/update',
		method: 'PUT',
		data
	})
}

// 删除生产任务
export function deleteWorkOrderApi(id) {
	return request({
		url: '/scm/mfg/work-order/delete?id=' + id,
		method: 'DELETE'
	})
}

// 导出生产任务 Excel
export function exportWorkOrderApi(params) {
	return request({
		url: '/scm/mfg/work-order/export-excel',
		method: 'GET',
		params
	})
}

// ==================== 子表（任务单明细） ====================

// 获得任务单明细列表
export function getWorkOrderDetailListByBizOrderIdApi(bizOrderId) {
	return request({
		url: '/scm/mfg/work-order/work-order-detail/list-by-biz-order-id?bizOrderId=' + bizOrderId,
		method: 'GET'
	})
}
